import mongoose, { Document, Schema } from 'mongoose';

export interface IDevice {
  deviceId: string;
  refreshToken: string;
  userAgent: string;
  ip: string;
  lastSeen: Date;
}

export interface IUser extends Document {
  email: string;
  passwordHash: string;
  fullName: string;
  devices: IDevice[];
  createdAt: Date;
  updatedAt: Date;
}

const deviceSchema = new Schema<IDevice>({
  deviceId: {
    type: String,
    required: true,
  },
  refreshToken: {
    type: String,
    required: true,
  },
  userAgent: {
    type: String,
    required: true,
  },
  ip: {
    type: String,
    required: true,
  },
  lastSeen: {
    type: Date,
    default: Date.now,
  },
});

const userSchema = new Schema<IUser>(
  {
    email: {
      type: String,
      required: true,
      unique: true,
      lowercase: true,
      trim: true,
    },
    passwordHash: {
      type: String,
      required: true,
    },
    fullName: {
      type: String,
      required: true,
      trim: true,
    },
    devices: [deviceSchema],
  },
  {
    timestamps: true,
  }
);

// Index for better query performance
// userSchema.index({ email: 1 });
// userSchema.index({ 'devices.deviceId': 1 });

export const User = mongoose.model<IUser>('User', userSchema);

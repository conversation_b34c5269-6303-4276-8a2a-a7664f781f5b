<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">
                    <i class="<%= categoryIcon %> me-2"></i>
                    <%= isEdit ? 'Edit' : 'Add' %> <%= categoryTitle.slice(0, -1) %>
                </h4>
            </div>
            <div class="card-body">
                <form id="transactionForm">
                    <div class="mb-3">
                        <label for="amount" class="form-label">Amount *</label>
                        <div class="input-group">
                            <span class="input-group-text">$</span>
                            <input type="number" 
                                   class="form-control" 
                                   id="amount" 
                                   name="amount" 
                                   step="0.01" 
                                   min="0" 
                                   required
                                   value="<%= transaction ? transaction.amount : '' %>">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">Description *</label>
                        <textarea class="form-control" 
                                  id="description" 
                                  name="description" 
                                  rows="3" 
                                  required
                                  placeholder="Enter a description for this transaction..."><%= transaction ? transaction.description : '' %></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="date" class="form-label">Date *</label>
                        <input type="date" 
                               class="form-control" 
                               id="date" 
                               name="date" 
                               required
                               value="<%= transaction ? transaction.date.toISOString().split('T')[0] : new Date().toISOString().split('T')[0] %>">
                    </div>
                    
                    <% if (categoryName !== 'incomes') { %>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Budget Allocation:</strong> 
                        <% if (categoryName === 'needs') { %>
                            25% of your income should go to needs (essentials like food, housing, utilities).
                        <% } else if (categoryName === 'wants') { %>
                            25% of your income should go to wants (entertainment, dining out, hobbies).
                        <% } else if (categoryName === 'investments') { %>
                            45% of your income should go to investments (savings, stocks, retirement).
                        <% } else if (categoryName === 'donations') { %>
                            5% of your income should go to donations (charity, giving back).
                        <% } %>
                    </div>
                    <% } %>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary" id="submitBtn">
                            <i class="fas fa-save me-2"></i>
                            <%= isEdit ? 'Update' : 'Save' %> <%= categoryTitle.slice(0, -1) %>
                        </button>
                        <a href="/<%= categoryName %>" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                        <% if (isEdit) { %>
                        <button type="button" class="btn btn-danger ms-auto" id="deleteBtn">
                            <i class="fas fa-trash me-2"></i>Delete
                        </button>
                        <% } %>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<% if (isEdit) { %>
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this <%= categoryTitle.slice(0, -1).toLowerCase() %>? This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">Delete</button>
            </div>
        </div>
    </div>
</div>
<% } %>

<script>
// Pass data to JavaScript
window.formData = {
    categoryName: '<%= categoryName %>',
    categoryTitle: '<%= categoryTitle %>',
    isEdit: <%= isEdit %>,
    transactionId: '<%= transaction ? transaction._id : '' %>'
};
</script>
<script src="/js/transaction-form.js"></script>

// Transactions page functionality
let currentPage = 1;
let currentFilters = {};
let deleteTransactionId = null;

document.addEventListener('DOMContentLoaded', function() {
    // For demo purposes, skip authentication check
    // if (!requireAuth()) return;

    initializeFilters();
    loadTransactions();

    // Event listeners
    document.getElementById('filterForm').addEventListener('submit', handleFilter);
    document.getElementById('clearFilters').addEventListener('click', clearFilters);
    const confirmDeleteBtn = document.getElementById('confirmDelete');
    if (confirmDeleteBtn) {
        confirmDeleteBtn.addEventListener('click', confirmDelete);
    }
});

function initializeFilters() {
    // Set default end date to today
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('endDate').value = today;
    
    // Set default start date to 30 days ago
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    document.getElementById('startDate').value = thirtyDaysAgo.toISOString().split('T')[0];
}

function handleFilter(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    currentFilters = {};
    
    for (const [key, value] of formData.entries()) {
        if (value.trim()) {
            currentFilters[key] = value;
        }
    }
    
    currentPage = 1;
    loadTransactions();
}

function clearFilters() {
    document.getElementById('filterForm').reset();
    initializeFilters();
    currentFilters = {};
    currentPage = 1;
    loadTransactions();
}

async function loadTransactions() {
    try {
        showLoading(true);

        // For demo purposes, use mock data
        const mockTransactions = generateMockTransactions();
        const mockPagination = {
            page: currentPage,
            limit: 25,
            total: mockTransactions.length,
            pages: Math.ceil(mockTransactions.length / 25)
        };

        renderTransactions(mockTransactions);
        renderPagination(mockPagination);
        updateSummary(mockTransactions, mockTransactions.length);

    } catch (error) {
        console.error('Error loading transactions:', error);
        showAlert('error', 'Error loading transactions');
    } finally {
        showLoading(false);
    }
}

function generateMockTransactions() {
    const categoryName = window.categoryData?.name || 'incomes';
    const descriptions = {
        incomes: ['Salary', 'Freelance Work', 'Investment Returns', 'Bonus', 'Side Business'],
        needs: ['Groceries', 'Rent', 'Utilities', 'Transportation', 'Insurance'],
        wants: ['Dining Out', 'Entertainment', 'Shopping', 'Hobbies', 'Travel'],
        investments: ['Stock Purchase', 'Mutual Fund', 'Retirement Fund', 'Savings Account', 'Crypto'],
        donations: ['Charity', 'Religious Organization', 'Community Fund', 'Disaster Relief', 'Education Fund']
    };

    const categoryDescriptions = descriptions[categoryName] || descriptions.incomes;
    const transactions = [];

    for (let i = 0; i < 15; i++) {
        const date = new Date();
        date.setDate(date.getDate() - Math.floor(Math.random() * 30));

        transactions.push({
            _id: `mock_${i}`,
            amount: Math.floor(Math.random() * 500) + 50,
            description: categoryDescriptions[Math.floor(Math.random() * categoryDescriptions.length)],
            date: date.toISOString(),
            createdAt: date.toISOString(),
            updatedAt: date.toISOString()
        });
    }

    return transactions.sort((a, b) => new Date(b.date) - new Date(a.date));
}

function renderTransactions(transactions) {
    const tbody = document.getElementById('transactionsTable');
    
    if (transactions.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="4" class="text-center text-muted py-4">
                    <i class="fas fa-inbox fa-2x mb-2"></i><br>
                    No transactions found
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = transactions.map(transaction => `
        <tr>
            <td>${formatDate(transaction.date)}</td>
            <td>${transaction.description}</td>
            <td class="text-end">${formatCurrency(transaction.amount)}</td>
            <td>
                <div class="btn-group btn-group-sm">
                    <a href="/${window.categoryData.name}/${transaction._id}/edit" 
                       class="btn btn-outline-primary">
                        <i class="fas fa-edit"></i>
                    </a>
                    <button type="button" 
                            class="btn btn-outline-danger" 
                            onclick="showDeleteModal('${transaction._id}')">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

function renderPagination(pagination) {
    const container = document.getElementById('paginationContainer');
    
    if (pagination.pages <= 1) {
        container.innerHTML = '';
        return;
    }
    
    let paginationHtml = '<ul class="pagination justify-content-center">';
    
    // Previous button
    paginationHtml += `
        <li class="page-item ${pagination.page === 1 ? 'disabled' : ''}">
            <button class="page-link" onclick="changePage(${pagination.page - 1})" 
                    ${pagination.page === 1 ? 'disabled' : ''}>
                Previous
            </button>
        </li>
    `;
    
    // Page numbers
    const startPage = Math.max(1, pagination.page - 2);
    const endPage = Math.min(pagination.pages, pagination.page + 2);
    
    if (startPage > 1) {
        paginationHtml += `
            <li class="page-item">
                <button class="page-link" onclick="changePage(1)">1</button>
            </li>
        `;
        if (startPage > 2) {
            paginationHtml += '<li class="page-item disabled"><span class="page-link">...</span></li>';
        }
    }
    
    for (let i = startPage; i <= endPage; i++) {
        paginationHtml += `
            <li class="page-item ${i === pagination.page ? 'active' : ''}">
                <button class="page-link" onclick="changePage(${i})">${i}</button>
            </li>
        `;
    }
    
    if (endPage < pagination.pages) {
        if (endPage < pagination.pages - 1) {
            paginationHtml += '<li class="page-item disabled"><span class="page-link">...</span></li>';
        }
        paginationHtml += `
            <li class="page-item">
                <button class="page-link" onclick="changePage(${pagination.pages})">${pagination.pages}</button>
            </li>
        `;
    }
    
    // Next button
    paginationHtml += `
        <li class="page-item ${pagination.page === pagination.pages ? 'disabled' : ''}">
            <button class="page-link" onclick="changePage(${pagination.page + 1})" 
                    ${pagination.page === pagination.pages ? 'disabled' : ''}>
                Next
            </button>
        </li>
    `;
    
    paginationHtml += '</ul>';
    container.innerHTML = paginationHtml;
}

function updateSummary(transactions, total) {
    const totalAmount = transactions.reduce((sum, t) => sum + t.amount, 0);
    const averageAmount = total > 0 ? totalAmount / total : 0;
    
    document.getElementById('totalAmount').textContent = formatCurrency(totalAmount);
    document.getElementById('totalCount').textContent = total;
    document.getElementById('averageAmount').textContent = formatCurrency(averageAmount);
}

function changePage(page) {
    currentPage = page;
    loadTransactions();
}

function showDeleteModal(transactionId) {
    deleteTransactionId = transactionId;
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}

async function confirmDelete() {
    if (!deleteTransactionId) return;
    
    try {
        const response = await apiRequest(`/${window.categoryData.name}/${deleteTransactionId}`, {
            method: 'DELETE'
        });
        
        if (response.ok) {
            showAlert('success', `${window.categoryData.title.slice(0, -1)} deleted successfully`);
            loadTransactions(); // Reload the list
            
            // Hide modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('deleteModal'));
            modal.hide();
        } else {
            const error = await response.json();
            showAlert('error', error.error || 'Failed to delete transaction');
        }
    } catch (error) {
        console.error('Delete error:', error);
        showAlert('error', 'Error deleting transaction');
    }
    
    deleteTransactionId = null;
}

function showLoading(show) {
    const table = document.getElementById('transactionsTable');
    if (show) {
        table.innerHTML = `
            <tr>
                <td colspan="4" class="text-center py-4">
                    <i class="fas fa-spinner fa-spin fa-2x"></i><br>
                    Loading transactions...
                </td>
            </tr>
        `;
    }
}
